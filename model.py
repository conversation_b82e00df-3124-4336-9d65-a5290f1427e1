"""
@authors: <PERSON>, et. al
@organization: GRASP Lab, University of Pennsylvania
@date: ...
@license: ...

@brief: This module contains the models that were used in the paper "Utilizing vision transformer models for end-to-end vision-based
quadrotor obstacle avoidance" by <PERSON><PERSON><PERSON><PERSON>, et. al
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import LSTM
import torch.nn.utils.spectral_norm as spectral_norm
from ViTsubmodules import *
from GoTsubmodules import GoTEncoderLayer

def refine_inputs(X):

    # fill quaternion rotation if not given
    # make it [1, 0, 0, 0] repeated with numrows = X[0].shape[0]
    if X[2] is None:
        # X[2] = torch.Tensor([1, 0, 0, 0]).float()
        X[2] = torch.zeros((X[0].shape[0], 4)).float().to(X[0].device)
        X[2][:, 0] = 1

    # if input depth images are not of right shape, resize
    if X[0].shape[-2] != 60 or X[0].shape[-1] != 90:
        X[0] = F.interpolate(X[0], size=(60, 90), mode='bilinear')

    return X

def depth_to_bins_lid(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6):
    """
    将深度图转换为深度分层索引，使用"远距离适度精细，近距离适度粗糙"的平衡分层策略

    核心思想：使用调节后的幂函数映射，实现平衡的深度分层，避免过度精细化

    分层特点：
    - 对于num_bins=6的情况（7个区间）：
      * 区间0: [0, 0.134]        - 近距离区间（适度粗糙）
      * 区间1: [0.134, 0.297]    - 中近距离区间
      * 区间2: [0.297, 0.487]    - 中距离区间
      * 区间3: [0.487, 0.693]    - 中远距离区间
      * 区间4: [0.693, 0.905]    - 远距离区间
      * 区间5: [0.905, 1.0]      - 极远距离区间（适度精细）

    数学原理：
    使用幂函数 f(t) = t^1.5，其中 t ∈ [0,1]
    这样可以确保：
    - 当 t 接近 0 时，f(t) 接近 0，变化适中（适度粗糙）
    - 当 t 接近 1 时，f(t) 接近 1，变化相对快但不过分（适度精细）
    - 避免了过度精细化导致的数据稀疏问题

    Args:
        depth_map (torch.Tensor): 输入深度图，形状为 [B, H, W] 或 [H, W]
        depth_min (float): 最小深度值（保留参数以维持接口兼容性）
        depth_max (float): 最大深度值（保留参数以维持接口兼容性）
        num_bins (int): 分层数量，默认为6（生成num_bins+1个区间）

    Returns:
        torch.Tensor: 深度分层索引，与输入形状相同，值范围为[0, num_bins]

    优势：
        1. 符合深度感知需求：远距离精细，近距离粗糙
        2. 物理意义明确：符合深度图像处理的实际需求
        3. 数值稳定：使用简单的二次函数，避免复杂运算
        4. 计算高效：O(1)的边界计算复杂度
        5. 灵活配置：支持不同num_bins值
    """
    # 生成分层边界，使用平衡的幂函数实现"远距离适度精细，近距离适度粗糙"
    split_points = []
    for i in range(1, num_bins):
        # 归一化位置：从 1/(num_bins+1) 到 num_bins/(num_bins+1)
        t = i / (num_bins)

        # 关键：使用调节后的幂函数 f(t) = t^1.5
        # 相比于 t^2，这提供了更温和的非线性分布：
        # - 近距离（t小）：边界值小，间隔相对大（适度粗糙）
        # - 远距离（t大）：边界值大，间隔相对小但不过分（适度精细）
        # - 避免了过度精细化的问题
        boundary = 1 - t ** 1.5

        split_points.append(boundary)

    # 构建完整的边界列表：[0, boundary1, boundary2, ..., boundaryN, 1]
    boundaries = [0.0] + split_points + [1.0]

    # 转换为张量，保持与输入相同的设备和数据类型
    boundaries = torch.tensor(boundaries, device=depth_map.device, dtype=depth_map.dtype)

    # 处理边界情况：将超出[0,1]范围的值裁剪到有效范围
    # 这确保了数值稳定性，避免异常输入导致的错误
    depth_map_clipped = torch.clamp(depth_map, 0.0, 1.0)

    # 使用 torch.searchsorted 进行高效的区间查找
    # searchsorted 返回每个深度值应该插入的位置，即对应的区间索引
    # 使用 boundaries[1:-1] 排除首尾边界，因为我们要找的是区间索引
    indices = torch.searchsorted(boundaries[1:-1], depth_map_clipped, right=False)

    # 确保索引在有效范围内 [0, num_bins]
    # 这是额外的安全检查，防止边界情况导致的索引越界
    indices = torch.clamp(indices, 0, num_bins-1)

    # 转换为长整型，适用于后续的embedding或分类操作
    return indices.long()

def split_depth_map_to_layers(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6, mask_value=-1.0):
    """
    将深度图按照LID方法分成多层，每层只包含属于该层的像素，其他位置用0填充

    Args:
        depth_map (torch.Tensor): 输入深度图，形状为 [B, H, W] 或 [H, W]
        depth_min (float): 最小深度值
        depth_max (float): 最大深度值
        num_bins (int): 分层数量
        Returns:
         torch.Tensor: 分层后的深度图，形状为 [B, num_bins, H, W] 或 [num_bins, H, W]
    """
    # 确保深度图至少是3维的 [B, H, W]
    if depth_map.dim() == 2:
         depth_map = depth_map.unsqueeze(0) # 添加批次维度

    B, C, H, W = depth_map.shape

    # 计算每个像素属于哪一层
    bin_indices = depth_to_bins_lid(depth_map, depth_min, depth_max, num_bins)
    bin_indices = bin_indices.long() # 转换为整数索引

    # 创建输出张量，形状为 [B, num_bins, H, W]
    # layered_depth_maps = torch.zeros((B, num_bins, H, W), device=depth_map.device)
    layered_depth_maps = torch.full((B, num_bins, H, W), mask_value, device=depth_map.device)

    # 为每一层创建掩码并填充深度值
    for b in range(B):
         for n in range(num_bins):
            # 创建当前层的掩码
            mask = (bin_indices[b] == n)

            # 将原始深度值填充到对应层的对应位置
            layered_depth_maps[b, n] = torch.where(mask, depth_map[b], torch.tensor(mask_value, device=depth_map.device))

    # 如果输入是2D的，则移除批次维度
    if depth_map.shape[0] == 1 and len(depth_map.shape) == 3:
        layered_depth_maps = layered_depth_maps.squeeze(0)

    return layered_depth_maps



class ConvNet(nn.Module):
    """
    Conv + FC Network
    Num Params: 235,269
    """
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv2d(1, 4, 3, 3)
        self.conv2 = nn.Conv2d(4, 10, 3, 2)
        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=1)
        self.maxpool = nn.MaxPool2d(2, 1)
        self.bn1 = nn.BatchNorm2d(4)

        self.fc0 = nn.Linear(845, 256, bias=False)
        self.fc1 = nn.Linear(256, 64, bias=False)
        self.fc2 = nn.Linear(64, 32, bias=False)
        self.fc3 = nn.Linear(32, 3)

    def forward(self, X):

        X = refine_inputs(X)

        x = X[0]
        # x = split_depth_map_to_layers(x)
        x = -self.maxpool(- self.bn1(F.relu(self.conv1(x))))
        x = self.avgpool(F.relu(self.conv2(x)))

        x = torch.flatten(x, 1)  # flatten all dimensions except batch

        metadata = torch.cat((X[1]*0.1, X[2]), dim=1).float()

        x = torch.cat((x, metadata), dim=1).float()

        x = F.leaky_relu(self.fc0(x))
        x = F.leaky_relu(self.fc1(x))
        x = torch.tanh(self.fc2(x))
        x = self.fc3(x)

        return x, None

class EncoderQ(nn.Module):
    def __init__(self, input_size, output_size):
        super(EncoderQ, self).__init__()
        self.fc = nn.Linear(input_size, output_size)

    def forward(self, x):
        return self.fc(x)


class Decoder(nn.Module):
    def __init__(self, input_size, output_size):
        super(Decoder, self).__init__()
        self.fc = nn.Linear(input_size, output_size)

    def forward(self, x):
        return self.fc(x)

class LSTMNet(nn.Module):
    """
    LSTM + FC Network
    Num Params: 2,949,937
    """
    def __init__(self):
        super().__init__()
        # 卷积层1：输入通道数为1，输出通道数为4，卷积核大小为5x5，步幅为3，填充为1
        self.conv1 = nn.Conv2d(1, 4, 5, stride=3, padding=1)
        # 卷积层2：输入通道数为4，输出通道数为10，卷积核大小为3x3，步幅为2，填充为0
        self.conv2 = nn.Conv2d(4, 10, 3, stride=2, padding=0)
        # 平均池化层：核大小为3x3，步幅为1
        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=1)
        # 最大池化层：核大小为3x3，步幅为1
        self.maxpool = nn.MaxPool2d(3, 1)
        # 批归一化层：对4个通道进行归一化
        self.bn1 = nn.BatchNorm2d(4)
        # 批归一化层：对10个通道进行归一化
        self.bn2 = nn.BatchNorm2d(10)

        # LSTM层：输入特征数调整为包含 encoderQ 输出、速度、四元组和隐状态的维度
        self.lstm_input_size = 395
        self.lstm_hidden_size = 395
        self.lstm = LSTM(input_size=self.lstm_input_size, hidden_size=self.lstm_hidden_size,
                         num_layers=2, dropout=0.15, bias=False)
        # encoderQ
        self.encoderQ = EncoderQ(660 + 395, 128)

        self.fcQ1 = spectral_norm(nn.Linear(128 + 1 + 4, 395))

        # decoder
        self.decoder = Decoder(395, 128)


        # 全连接层：输入特征数为395，输出特征数为64
        self.fc1 = spectral_norm(nn.Linear(395 + 128, 64))
        # 全连接层：输入特征数为64，输出特征数为16
        self.fc2 = spectral_norm(nn.Linear(64, 16))
        # 全连接层：输入特征数为16，输出特征数为3（最终输出）
        self.fc3 = spectral_norm(nn.Linear(16, 3))

    def forward(self, X):
        # 预处理输入数据，确保深度图大小为60x90，若四元数为 None 则进行填充
        X = refine_inputs(X)
        batch_size = X[0].shape[0]

        # 提取深度图像，形状为 (B, 1, 60, 90)，B 为批次大小
        x = X[0]

        # 输出形状变为 (B, 4, 20, 29)
        x = F.relu(self.conv1(x))
        # 经过批归一化层 1，形状不变，仍为 (B, 4, 20, 29)
        x = self.bn1(x)
        # 输出形状变为 (B, 4, 18, 27)
        x = -self.maxpool(-x)

        # 输出形状变为 (B, 10, 8, 13)
        x = F.relu(self.conv2(x))
        # 经过批归一化层 2，形状不变，仍为 (B, 10, 8, 13)
        x = self.bn2(x)

        # 输出形状变为 (B, 10, 6, 11)
        x = self.avgpool(x)

        # 将特征图展平为一维向量，形状变为 (B, 10 * 6 * 11 = 660)
        x = torch.flatten(x, 1)

        if len(X) > 4:
            x = self.encoderQ(torch.cat(x, X[4]), dim=1).float() # 输入为 (B, 660 + 395)，输出为 (B, 128)
        else:
            l = torch.zeros(batch_size, self.lstm_input_size).to(X[0].device)
            x = self.encoderQ(torch.cat((x, l), dim=1), dim=1).float() #输入为 (B, 660 + 395)，输出为 (B, 128)


        # 连接后的形状变为 (B, 128 + 1 + 4 = 133)
        x = torch.cat((x, X[1] * 0.1, X[2]), dim=1).float()

        x = self.fcQ1(x) # 输入为 (B, 133)，输出为 (B, 395)

        # 如果提供了隐藏状态，则传入 LSTM
        if len(X) > 3:
            # 输入形状为 (B, 395)，输出形状为 (B, 395)
            l, h = self.lstm(x, X[3])
        else:
            # 输入形状为 (B, 395)，输出形状为 (B, 395)
            l, h = self.lstm(x)

        d = self.decoder(l) # 输入为 (B, 395)，输出为 (B, 128)
        x = torch.cat((d, l), dim=1).float()#  连接后的形状变为 (B, 128 + 395)
        # 经过全连接层 1，输入特征数 395 + 128，输出特征数 64
        # 形状变为 (B, 64)
        x = F.leaky_relu(self.fc1(x))

        # 经过全连接层 2，输入特征数 64，输出特征数 16
        # 形状变为 (B, 16)
        x = F.leaky_relu(self.fc2(x))

        # 经过全连接层 3，输入特征数 16，输出特征数 3
        # 形状变为 (B, 3)
        x = self.fc3(x)

        return x, h, l,

class LSTMNetVIT(nn.Module):
    """
    ViT+LSTM Network
    结合了 Vision Transformer 的特征提取能力和 LSTM 的时序建模能力
    功能：
        1. 使用 MixTransformerEncoderLayer 提取深度图像特征
        2. 通过解码器将特征映射到较低维度
        3. 结合期望速度和四元数信息
        4. 使用 LSTM 进行时序建模
        5. 通过全连接层生成控制命令
    输入：
        X: 列表形式的输入数据，包含深度图像、期望速度、四元数和可选的隐藏状态
        其中深度图像形状为 (B, 1, 60, 90)，B 为批次大小
    输出：
        控制命令(3维向量)和 LSTM 隐藏状态
    Num Params: 3,563,663
    """
    def __init__(self):
        super().__init__()
        self.SEAttention = SEModule(6)
        self.encoder_blocks = nn.ModuleList([
            MixTransformerEncoderLayer(6, 32, patch_size=7, stride=4, padding=3, n_layers=2, reduction_ratio=8, num_heads=1, expansion_factor=8),
            MixTransformerEncoderLayer(32, 64, patch_size=3, stride=2, padding=1, n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=8)
        ])

        self.decoder = spectral_norm(nn.Linear(4608, 512))
        self.lstm = (nn.LSTM(input_size=517, hidden_size=128,
                         num_layers=3, dropout=0.1))
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))

        self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48,12,3, padding = 1)

    def forward(self, X):

        X = refine_inputs(X)

        x = X[0]
        x = split_depth_map_to_layers(x)
        x = self.SEAttention(x)
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))
        out = embeds[1:]
        out = torch.cat([self.pxShuffle(out[1]),self.up_sample(out[0])],dim=1)
        out = self.down_sample(out)
        out = self.decoder(out.flatten(1))
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()
        if len(X)>3:
            out,h = self.lstm(out, X[3])
        else:
            out,h = self.lstm(out)
        out = self.nn_fc2(out)
        return out, h

class ViT(nn.Module):
    """
    ViT+FC Network
    Num Params: 3,101,199
    """
    def __init__(self):
        super().__init__()
        self.SEAttention = SEModule(6)
        # self.fc1 = nn.Linear(5400, 512)
        # self.fc2 = nn.Linear(512, 5400)
        # self.lstm = (nn.LSTM(input_size=512, hidden_size=128,
        #                  num_layers=3, dropout=0.1))
        self.encoder_blocks = nn.ModuleList([
            HeatMixTransformerEncoderLayer(6, 32, patch_size=7, stride=4, padding=3, n_layers=2, reduction_ratio=8, num_heads=1, expansion_factor=8),
            HeatMixTransformerEncoderLayer(32, 64, patch_size=3, stride=2, padding=1, n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=8)
        ])
        self.decoder = nn.Linear(4608, 512)
        self.nn_fc1 = spectral_norm(nn.Linear(517, 256))
        self.nn_fc2 = spectral_norm(nn.Linear(256, 3))
        self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48,12,3, padding = 1)

    def forward(self, X):

        X = refine_inputs(X)

        x = X[0]
        x = split_depth_map_to_layers(x)
        # B, C, H, W = x.shape
        x = self.SEAttention(x)
        # x = x.reshape(B,C,-1) # 形状为[B, C, H*W]
        # x = self.fc1(x)
        # x = self.lstm(x)
        # x = self.fc2(x)
        # x = x.reshape(B,C,H,W)
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))
        out = embeds[1:]
        out = torch.cat([self.pxShuffle(out[1]),self.up_sample(out[0])],dim=1)
        out = self.down_sample(out)
        out = self.decoder(out.flatten(1))
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()
        out = F.leaky_relu(self.nn_fc1(out))
        out = self.nn_fc2(out)

        return out, None

class UNetConvLSTMNet(nn.Module):
    """
    UNet+LSTM Network
    Num Params: 2,955,822
    """

    def __init__(self):
        super().__init__()

        self.unet_e11 = nn.Conv2d(1, 4, kernel_size=3, padding=1)
        self.unet_e12 = nn.Conv2d(4, 4, kernel_size=3, padding=1) #(N, 4, 60, 90)
        self.unet_pool1 = nn.MaxPool2d(kernel_size=2, stride=3,) #(N, 4, 30, 45)

        self.unet_e21 = nn.Conv2d(4, 8, kernel_size=3, padding=1) #(N, 8, 26, 41)
        self.unet_e22 = nn.Conv2d(8, 8, kernel_size=3, padding=1) #(N, 8, 24, 39)
        self.unet_pool2 = nn.MaxPool2d(kernel_size=2, stride=2,) #(N, 8, 12, 19)

        #Input: (N, 8, 12, 19)
        self.unet_e31 = nn.Conv2d(8, 16, kernel_size=3, padding=1) #(N, 8, 10, 17)
        self.unet_e32 = nn.Conv2d(16, 16, kernel_size=3, padding=1) #(N, 16, 8, 15)

        self.unet_upconv1 = nn.ConvTranspose2d(16, 8, kernel_size=2, stride=2,)
        self.unet_d11 = nn.Conv2d(16, 8, kernel_size=3, padding=1)
        self.unet_d12 = nn.Conv2d(8, 8, kernel_size=3, padding=1)

        self.unet_upconv2 = nn.ConvTranspose2d(8, 4, kernel_size=3, stride=3,)
        self.unet_d21 = nn.Conv2d(8, 4, kernel_size=3, padding=1)
        self.unet_d22 = nn.Conv2d(4, 4, kernel_size=3, padding=1)

        self.unet_out = nn.Conv2d(4, 1, kernel_size=1)

        self.conv_conv1 = nn.Conv2d(2, 4, 5, 3)
        self.conv_conv2 = nn.Conv2d(4, 10, 5, 2)
        self.conv_avgpool = nn.AvgPool2d(kernel_size=2, stride=1)
        self.conv_maxpool = nn.MaxPool2d(2, 1)
        self.conv_bn1 = nn.BatchNorm2d(4)

        self.lstm = LSTM(input_size=3065, hidden_size=200, num_layers=2, dropout=0.15, bias=False)

        self.nn_fc1 = torch.nn.utils.spectral_norm(nn.Linear(200, 64))
        self.nn_fc2 = torch.nn.utils.spectral_norm(nn.Linear(64, 32))
        self.nn_fc3 = torch.nn.utils.spectral_norm(nn.Linear(32, 3))

    def forward(self, X):

        X = refine_inputs(X)

        img, des_vel, quat = X[0], X[1], X[2]
        y_e1 = torch.relu(self.unet_e12(torch.relu(self.unet_e11(img))))
        unet_enc1 = self.unet_pool1(y_e1)
        y_e2 = torch.relu(self.unet_e22(torch.relu(self.unet_e21(unet_enc1))))
        unet_enc2 = self.unet_pool2(y_e2)
        y_e3 = torch.relu(self.unet_e32(torch.relu(self.unet_e31(unet_enc2))))

        unet_dec1 = torch.relu(self.unet_d12(torch.relu(self.unet_d11(torch.cat([self.unet_upconv1(y_e3), y_e2], dim=1)))))
        unet_dec2 = torch.relu(self.unet_d22(torch.relu(self.unet_d21(torch.cat([self.unet_upconv2(unet_dec1), y_e1], dim=1)))))

        y_unet = self.unet_out(unet_dec2)
        x_conv = torch.cat((img, y_unet), dim=1)

        y_conv = -self.conv_maxpool(-torch.relu(self.conv_bn1(self.conv_conv1(x_conv))))
        y_conv = self.conv_avgpool(torch.relu(self.conv_conv2(y_conv)))

        x_lstm = torch.cat([torch.flatten(y_conv, 1), torch.flatten(y_e3, 1), des_vel*0.1, quat], dim=1).float()

        if len(X)>3:
            y_lstm, h = self.lstm(x_lstm, X[3])
        else:
            y_lstm, h = self.lstm(x_lstm)


        y_fc1 = F.leaky_relu(self.nn_fc1(y_lstm))
        y_fc2 = F.leaky_relu(self.nn_fc2(y_fc1))
        y = self.nn_fc3(y_fc2)

        return y, h

class CustomNet(nn.Module):
    """
    自定义网络模型，结合CNN和Transformer特点
    """
    def __init__(self):
        super().__init__()
        # CNN部分用于特征提取
        self.conv1 = nn.Conv2d(1, 16, kernel_size=5, stride=2, padding=2)
        self.bn1 = nn.BatchNorm2d(16)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3, stride=2, padding=1)
        self.bn2 = nn.BatchNorm2d(32)

        # 使用一个简化版的Transformer编码器
        self.encoder_block = MixTransformerEncoderLayer(
            32, 64, patch_size=3, stride=1, padding=1,
            n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=4
        )

        # 解码器部分
        self.decoder = nn.Linear(4608, 256)
        self.fc1 = spectral_norm(nn.Linear(261, 128))  # 256 + 1 (速度) + 4 (四元数)
        self.fc2 = spectral_norm(nn.Linear(128, 64))
        self.fc3 = spectral_norm(nn.Linear(64, 3))

        # LSTM部分用于时序建模
        self.lstm = nn.LSTM(input_size=261, hidden_size=128, num_layers=2, dropout=0.1)

    def forward(self, X):
        X = refine_inputs(X)

        x = X[0]  # 图像输入

        # CNN特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))

        # Transformer编码
        x = self.encoder_block(x)

        # 展平特征
        x = self.decoder(x.flatten(1))

        # 连接元数据（速度和四元数）
        x = torch.cat([x, X[1]/10, X[2]], dim=1).float()

        # 使用LSTM进行时序建模（如果提供了隐藏状态）
        if len(X) > 3:
            x, h = self.lstm(x, X[3])
        else:
            x, h = self.lstm(x)

        # 全连接层进行最终预测
        x = F.leaky_relu(self.fc1(x))
        x = F.leaky_relu(self.fc2(x))
        x = self.fc3(x)

        return x, h

class WindowsViT(nn.Module):
    """
    WindowsViT模型：结合窗口注意力机制和Transformer的视觉模型
    功能：
        1. 使用MixWindowsTransformerEncoderLayer提取图像特征
        2. 通过解码器和全连接层生成控制命令
    输入：
        X: 列表形式的输入数据，包含深度图像、期望速度和四元数
        其中深度图像形状为 (B, 1, 60, 90)，B 为批次大小
    输出：
        控制命令(3维向量)和None(兼容其他模型接口)
    """

    def __init__(self):
        super().__init__()
        # Vision Transformer编码器块
        # 输入图像经过两层编码器，每层包含多个窗口注意力模块
        self.encoder_blocks = nn.ModuleList([
            MixWindowsTransformerEncoderLayer(1, 32, patch_size=7, stride=3, padding=3,
                                      n_layers=2, reduction_ratio=7, num_heads=[1, 4], expansion_factor=8, window_size=[2, 10]),
            MixWindowsTransformerEncoderLayer(32, 64, patch_size=3, stride=1, padding=1,
                                      n_layers=2, reduction_ratio=3, num_heads=[2, 8], expansion_factor=8, window_size=[2, 10])
        ])
        # 解码器：将编码器输出的特征映射到较低维度
        self.decoder = nn.Linear(1200, 512)  # 输入维度为1200，输出维度为512
        # 全连接层：进一步处理特征并生成控制命令
        self.nn_fc1 = spectral_norm(nn.Linear(517, 256))  # 输入维度为517(512+5+4)，输出维度为256
        self.nn_fc2 = spectral_norm(nn.Linear(256, 3))  # 输入维度为256，输出维度为3(控制命令)

        # 特征处理层：用于调整特征图的空间尺寸
        self.up_sample = nn.Upsample(size=(16, 24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48, 12, 3, padding=1)

    def forward(self, X):
        """
        前向传播函数
        参数：
            X: 输入数据列表 [深度图像, 期望速度, 四元数]
        返回：
            控制命令(3维向量)和None(兼容其他模型接口)
        """
        # 预处理输入数据
        X = refine_inputs(X)

        # 提取深度图像，初始形状为 (B, 1, 60, 90)，B 为批次大小
        x = X[0]

        # 通过Vision Transformer编码器块
        # x = split_depth_map_to_layers(x)
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))

        # 处理编码器输出(跳过原始输入)
        out = embeds[1:]
        out[0] = out[0].mean(dim=1, keepdim=True)  # 第一个编码器输出在通道维度取平均，形状从 (B, 32, 20, 30) 变为 (B, 1, 20, 30)
        out[1] = out[1].mean(dim=1, keepdim=True)  # 第二个编码器输出在通道维度取平均，形状从 (B, 64, 20, 30) 变为 (B, 1, 20, 30)
        out = torch.cat(out, dim=1)  # 拼接两个编码器输出，形状变为 (B, 2, 20, 30)

        # 将特征展平并通过解码器
        out = self.decoder(out.flatten(1))  # 展平后形状变为 (B, 2 * 20 * 30 = 1200)，经过解码器后变为 (B, 512)

        # 连接特征和元数据(期望速度和四元数)
        # 期望速度形状为 (B, 5)，四元数形状为 (B, 4)
        out = torch.cat([out, X[1] / 10, X[2]], dim=1).float()  # 输入维度变为 (B, 517)（512+5+4）

        # 通过全连接层和激活函数
        out = F.leaky_relu(self.nn_fc1(out))  # 输入维度为 (B, 517)，输出维度为 (B, 256)
        out = self.nn_fc2(out)  # 输入维度为 (B, 256)，输出维度为 (B, 3)（控制命令）

        # 返回控制命令和None(兼容其他模型接口)
        return out, None

class LSTMWindowsViT(nn.Module):
    """
    WindowsViT+LSTM Network
    结合了WindowsViT的窗口注意力机制和LSTM的时序建模能力
    """
    def __init__(self):
        super().__init__()
        # Vision Transformer编码器块，与WindowsViT中的相同
        self.encoder_blocks = nn.ModuleList([
            MixWindowsTransformerEncoderLayer(1, 32, patch_size=7, stride=3, padding=3,
                                      n_layers=2, reduction_ratio=7, num_heads=[1, 4], expansion_factor=8, window_size=[2, 10]),
            MixWindowsTransformerEncoderLayer(32, 64, patch_size=3, stride=1, padding=1,
                                      n_layers=2, reduction_ratio=3, num_heads=[2, 8], expansion_factor=8, window_size=[2, 10])
        ])
        # 解码器
        self.decoder = nn.Linear(1200, 512)  # 将ViT特征映射到较低维度，注意这里从1200改为800

        # LSTM层，用于时序建模
        self.lstm = nn.LSTM(input_size=517, hidden_size=128,
                         num_layers=3, dropout=0.1)

        # 最终输出层
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))  # 输出3维控制命令

        # 特征处理层
        # self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        # self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        # self.down_sample = nn.Conv2d(48,12,3, padding=1)



    def forward(self, X):
        """
        前向传播函数

        参数:
            X: 输入数据列表 [深度图像, 期望速度, 四元数, 可选的隐藏状态]

        返回:
            控制命令(3维向量)和LSTM隐藏状态
        """
        # 预处理输入
        X = refine_inputs(X)

        # 提取深度图像
        x = X[0]


        # 通过Vision Transformer编码器块
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))

        # 处理编码器输出(跳过原始输入)
        out = embeds[1:]
        del embeds

        out[0] = out[0].mean(dim=1, keepdim=True)
        out[1] = out[1].mean(dim=1, keepdim=True)
        out = torch.cat(out, dim=1)

        # 将特征展平并通过解码器
        out = self.decoder(out.flatten(1))

        # 连接特征和元数据(期望速度和四元数)
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()

        # 通过LSTM层进行时序建模
        if len(X) > 3:
            out, h = self.lstm(out, X[3])
        else:
            out, h = self.lstm(out)

        # 最终输出层
        out = self.nn_fc2(out)

        # 返回控制命令和LSTM隐藏状态
        return out, h



class LSTMWindowsGoT(nn.Module):
    """
    WindowsViT+LSTM+GoT Network
    结合了WindowsViT的窗口注意力机制、LSTM的时序建模能力和GoT的图像-四元数融合能力

    特点：
    1. 第一层使用MixWindowsTransformerEncoderLayer处理图像
    2. 第二层使用GoTTransformerEncoderLayer处理图像和四元数
    3. GoT层将图像切成6个10x10尺寸的小窗口进行处理
    """
    def __init__(self):
        super().__init__()
        # 第一个Vision Transformer编码器块，与LSTMWindowsViT中的相同


        self.encoder_block1 = MixWindowsTransformerEncoderLayer(1, 32, patch_size=7, stride=3, padding=3,
                                  n_layers=2, reduction_ratio=7, num_heads=[1, 2], expansion_factor=8, window_size=[2, 10])

        # 第二个编码器块使用GoT Transformer，可以处理图像和四元数
        self.encoder_block2 = GoTEncoderLayer(32, 32, patch_size=3, stride=1, padding=1,
                                n_layers=2, num_heads=8, dim=64, window_size=10)

        # 解码器
        self.fc1 = nn.Linear(64, 600)
        self.decoder = nn.Linear(1200, 512)  # 将特征映射到较低维度

        # LSTM层，用于时序建模
        self.lstm = nn.LSTM(input_size=513, hidden_size=128,
                         num_layers=3, dropout=0.1)

        # 最终输出层
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))  # 输出3维控制命令

    def forward(self, X):
        """
        前向传播函数

        参数:
            X: 输入数据列表 [深度图像, 期望速度, 四元数, 可选的隐藏状态]

        返回:
            控制命令(3维向量)和LSTM隐藏状态
        """
        # 预处理输入
        X = refine_inputs(X)
        # 提取深度图像和四元数
        x = X[0]
        quat = X[2]
        # 通过第一个Vision Transformer编码器块
        x1 = self.encoder_block1(x)# [b, c, h, w]
        # 通过GoT Transformer编码器块，同时处理图像特征和四元数
        x2 = self.encoder_block2(x1, quat) # [b, n, dim]
        x1 = x1.mean(dim=1).flatten(1)#[b,dim]
        x2 = x2[:, 0] # [b, dim]
        x2 = self.fc1(x2)
        out = torch.cat([x1, x2], dim=1)
        # 将特征展平并通过解码器
        out = self.decoder(out)
        # 连接特征和元数据(期望速度和四元数)
        out = torch.cat([out, X[1]/10], dim=1).float()
        # 通过LSTM层进行时序建模
        if len(X) > 3:
            out, h = self.lstm(out, X[3])
        else:
            out, h = self.lstm(out)

        # 最终输出层
        out = self.nn_fc2(out)

        # 返回控制命令和LSTM隐藏状态
        return out, h


class LSTMNetVIT_ChannelLSTM(nn.Module):
    """
    ViT+Channel-wise LSTM Network

    新的模型架构，将LSTM组件移动到深度图处理流程的后期阶段

    架构特点：
    1. 深度图分层处理：split_depth_map_to_layers() -> SEAttention()
    2. 通道级LSTM：对每个深度层分别应用LSTM进行时序建模
    3. 参数优化策略：通道分组、权重共享、降维处理
    4. 支持非正方形深度图像：[B, 15*23, 32] 输入格式

    参数控制策略：
    - 通道分组：6个深度层分为3组，每组2个通道共享LSTM参数
    - 降维处理：在LSTM前后添加降维和升维层，减少计算量
    - 隐藏层优化：使用较小的hidden_size以控制总参数量
    - 权重共享：多个通道组使用相同的LSTM权重

    预期参数量：与原LSTMNetVIT相近（约3.5M参数）

    输入：
        X: 列表形式的输入数据，包含深度图像、期望速度、四元数和可选的隐藏状态
        支持非正方形深度图像，输入格式为[B, 15*23, 32]
    输出：
        控制命令(3维向量)和LSTM隐藏状态
    """

    def __init__(self,
                 num_depth_layers=6,      # 深度分层数量
                 channel_groups=3,        # 通道分组数量
                 lstm_hidden_size=64,     # LSTM隐藏层大小（减小以控制参数量）
                 lstm_num_layers=2,       # LSTM层数
                 feature_dim_reduction=4, # 特征降维比例
                 dropout=0.1):
        """
        初始化通道级LSTM模型

        Args:
            num_depth_layers: 深度分层数量，默认6层
            channel_groups: 通道分组数量，默认3组（每组2个通道）
            lstm_hidden_size: LSTM隐藏层大小，默认64（原模型为128）
            lstm_num_layers: LSTM层数，默认2层（原模型为3层）
            feature_dim_reduction: 特征降维比例，默认4倍降维
            dropout: Dropout比例
        """
        super().__init__()

        # 保存配置参数
        self.num_depth_layers = num_depth_layers
        self.channel_groups = channel_groups
        self.channels_per_group = num_depth_layers // channel_groups
        self.lstm_hidden_size = lstm_hidden_size
        self.feature_dim_reduction = feature_dim_reduction

        # 原始ViT组件（与LSTMNetVIT相同）
        self.SEAttention = SEModule(num_depth_layers)
        self.encoder_blocks = nn.ModuleList([
            MixTransformerEncoderLayer(num_depth_layers, 32, patch_size=7, stride=4, padding=3,
                                     n_layers=2, reduction_ratio=8, num_heads=1, expansion_factor=8),
            MixTransformerEncoderLayer(32, 64, patch_size=3, stride=2, padding=1,
                                     n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=8)
        ])

        # 特征处理组件（与LSTMNetVIT相同）
        self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48, 12, 3, padding=1)

        # 计算ViT特征输出维度（与原模型相同）
        self.vit_feature_dim = 4608  # 12 * 16 * 24 = 4608

        # 新增：通道级LSTM组件
        # 策略1：特征降维层，减少LSTM输入维度
        self.channel_feature_reducer = nn.Linear(self.vit_feature_dim,
                                                self.vit_feature_dim // feature_dim_reduction)
        reduced_feature_dim = self.vit_feature_dim // feature_dim_reduction

        # 策略2：共享LSTM权重，每个通道组使用相同的LSTM
        # 创建一个LSTM，所有通道组共享
        self.shared_channel_lstm = nn.LSTM(
            input_size=reduced_feature_dim,
            hidden_size=lstm_hidden_size,
            num_layers=lstm_num_layers,
            dropout=dropout if lstm_num_layers > 1 else 0,
            batch_first=True
        )

        # 策略3：特征升维层，恢复特征维度用于后续处理
        self.channel_feature_expander = nn.Linear(lstm_hidden_size, reduced_feature_dim)

        # 通道特征融合层：将多个通道的LSTM输出融合
        self.channel_fusion = nn.Linear(reduced_feature_dim * num_depth_layers, 512)

        # 通道适配器：用于调整LSTM输出的通道数
        self.channel_adapter = nn.Conv2d(reduced_feature_dim, num_depth_layers, 1)

        # 最终的全局LSTM（保持与原模型相似的结构）
        self.global_lstm = nn.LSTM(input_size=517, hidden_size=128,  # 512 + 1 + 4 = 517
                                 num_layers=2, dropout=0.1)

        # 输出层
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))

        # 用于调整输入格式以支持非正方形深度图像
        self.input_adapter = nn.AdaptiveAvgPool2d((60, 90))  # 将[B, 1, 15, 23]调整为[B, 1, 60, 90]

    def forward(self, X):
        """
        前向传播函数 - 实现通道级LSTM的深度图处理流程

        处理流程：
        1. 输入预处理和格式调整（支持非正方形输入）
        2. 深度图分层处理：split_depth_map_to_layers()
        3. SE注意力机制：SEAttention()
        4. **新增**：通道级LSTM时序建模（核心创新点）
        5. ViT特征提取：encoder_blocks
        6. 特征融合和解码
        7. 全局LSTM和最终输出

        Args:
            X: 输入数据列表 [深度图像, 期望速度, 四元数, 可选的隐藏状态]
               深度图像支持多种格式：
               - 标准格式：[B, 1, 60, 90]
               - 非正方形格式：[B, 1, 15, 23] 或其他尺寸

        Returns:
            tuple: (控制命令, LSTM隐藏状态)
                - 控制命令: [B, 3] 三维控制向量
                - LSTM隐藏状态: 用于时序连续性
        """
        # 步骤1：输入预处理
        X = refine_inputs(X)

        # 支持非正方形深度图像输入格式调整
        x = X[0]  # [B, 1, H, W]
        if x.shape[-2:] != (60, 90):
            # 如果输入不是标准尺寸，使用自适应池化调整
            x = self.input_adapter(x)  # [B, 1, 60, 90]

        # 步骤2：深度图分层处理（与原LSTMNetVIT相同）
        x = split_depth_map_to_layers(x)  # [B, 6, 60, 90]

        # 步骤3：SE注意力机制（与原LSTMNetVIT相同）
        x = self.SEAttention(x)  # [B, 6, 60, 90]

        # 步骤4：**核心创新** - 通道级LSTM时序建模
        # 这是新模型的关键特性：在SE注意力后对每个深度层应用LSTM
        x = self._apply_channel_lstm(x)  # [B, 6, 60, 90] -> [B, 6, 60, 90]

        # 步骤5：ViT特征提取（与原LSTMNetVIT相同）
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))

        # 步骤6：特征融合和解码（与原LSTMNetVIT相同）
        out = embeds[1:]  # 跳过原始输入
        out = torch.cat([self.pxShuffle(out[1]), self.up_sample(out[0])], dim=1)
        out = self.down_sample(out)  # [B, 12, 16, 24]

        # 特征展平和融合
        out = self.channel_fusion(out.flatten(1))  # [B, 4608] -> [B, 512]

        # 步骤7：连接元数据并进行全局LSTM处理
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()  # [B, 517]

        # 全局LSTM时序建模
        if len(X) > 3:
            out, h = self.global_lstm(out, X[3])
        else:
            out, h = self.global_lstm(out)

        # 最终输出
        out = self.nn_fc2(out)  # [B, 3]

        return out, h

    def _apply_channel_lstm(self, x):
        """
        对每个深度通道分别应用LSTM进行时序建模（优化版本）

        这是新模型的核心创新点：
        1. 将深度图的每个层视为一个时序特征通道
        2. 对每个通道分别应用LSTM，捕获深度层间的时序关系
        3. 通过参数共享和降维策略控制参数量
        4. 使用批量处理优化计算效率

        Args:
            x: [B, num_depth_layers, H, W] 深度分层特征图

        Returns:
            [B, num_depth_layers, H, W] 经过通道LSTM处理的特征图
        """
        B, C, H, W = x.shape  # [B, 6, 60, 90]

        # 优化策略：批量处理所有空间位置
        # [B, C, H, W] -> [B*H*W, C] 将所有空间位置展平为批次维度
        x_reshaped = x.permute(0, 2, 3, 1).contiguous()  # [B, H, W, C]
        x_flat = x_reshaped.view(-1, C)  # [B*H*W, C]

        # 降维处理以减少LSTM参数量
        x_reduced = self.channel_feature_reducer(x_flat)  # [B*H*W, reduced_dim]

        # 为LSTM添加序列维度：[B*H*W, reduced_dim] -> [B*H*W, 1, reduced_dim]
        x_lstm_input = x_reduced.unsqueeze(1)  # [B*H*W, 1, reduced_dim]

        # 应用共享的通道LSTM（批量处理）
        lstm_out, _ = self.shared_channel_lstm(x_lstm_input)  # [B*H*W, 1, hidden_size]

        # 移除序列维度并升维恢复特征维度
        lstm_out_flat = lstm_out.squeeze(1)  # [B*H*W, hidden_size]
        lstm_out_expanded = self.channel_feature_expander(lstm_out_flat)  # [B*H*W, reduced_dim]

        # 恢复空间维度：[B*H*W, reduced_dim] -> [B, H, W, reduced_dim] -> [B, reduced_dim, H, W]
        output_reshaped = lstm_out_expanded.view(B, H, W, -1)  # [B, H, W, reduced_dim]
        output_spatial = output_reshaped.permute(0, 3, 1, 2).contiguous()  # [B, reduced_dim, H, W]

        # 通过预定义的通道适配器恢复到原始通道数
        if output_spatial.shape[1] != C:
            output_spatial = self.channel_adapter(output_spatial)

        return output_spatial

    def get_model_info(self):
        """
        获取模型配置信息和参数统计

        Returns:
            dict: 包含模型配置和参数统计的字典
        """
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        # 分析各组件参数量
        component_params = {}
        for name, module in self.named_children():
            module_params = sum(p.numel() for p in module.parameters() if p.requires_grad)
            component_params[name] = module_params

        return {
            'model_name': 'LSTMNetVIT_ChannelLSTM',
            'total_params': total_params,
            'component_params': component_params,
            'config': {
                'num_depth_layers': self.num_depth_layers,
                'channel_groups': self.channel_groups,
                'channels_per_group': self.channels_per_group,
                'lstm_hidden_size': self.lstm_hidden_size,
                'feature_dim_reduction': self.feature_dim_reduction,
            },
            'architecture_features': [
                '通道级LSTM时序建模',
                '参数共享策略',
                '特征降维优化',
                '支持非正方形深度图像',
                '与原LSTMNetVIT接口兼容'
            ]
        }


if __name__ == '__main__':
    print("MODEL NUM PARAMS ARE")
    model = ConvNet().float()
    print("ConvNet: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMNet().float()
    print("LSTMNet: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = UNetConvLSTMNet().float()
    print("UNET: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = ViT().float()
    print("VIT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMNetVIT().float()
    print("VITLSTM: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = CustomNet().float()
    print("CustomNet: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = WindowsViT().float()
    print("WindowsViT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMWindowsViT().float()
    print("LSTMWindowsViT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMWindowsGoT().float()
    print("LSTMWindowsGoT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMNetVIT_ChannelLSTM().float()
    print("LSTMNetVIT_ChannelLSTM: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    # 显示新模型的详细信息
    model_info = model.get_model_info()
    print(f"\n{model_info['model_name']} 详细信息:")
    print(f"总参数量: {model_info['total_params']:,}")
    print("各组件参数分布:")
    for component, params in model_info['component_params'].items():
        percentage = (params / model_info['total_params']) * 100
        print(f"  {component}: {params:,} ({percentage:.1f}%)")
    print("架构特性:")
    for feature in model_info['architecture_features']:
        print(f"  - {feature}")

    # 测试非正方形输入兼容性
    print(f"\n测试非正方形深度图像输入兼容性:")
    test_inputs = [
        torch.randn(2, 1, 15, 23),  # 非正方形输入
        torch.randn(2, 1),          # 期望速度
        torch.randn(2, 4)           # 四元数
    ]

    try:
        with torch.no_grad():
            output, hidden = model(test_inputs)
            print(f"✓ 非正方形输入测试成功")
            print(f"  输入形状: {test_inputs[0].shape}")
            print(f"  输出形状: {output.shape}")
            print(f"  隐藏状态形状: {hidden[0].shape if hidden else 'None'}")
    except Exception as e:
        print(f"✗ 非正方形输入测试失败: {e}")

    # 对比原始LSTMNetVIT和新模型的参数量
    original_model = LSTMNetVIT().float()
    original_params = sum(p.numel() for p in original_model.parameters() if p.requires_grad)
    new_params = model_info['total_params']

    print(f"\n参数量对比:")
    print(f"原始LSTMNetVIT: {original_params:,}")
    print(f"新LSTMNetVIT_ChannelLSTM: {new_params:,}")
    print(f"参数变化: {((new_params - original_params) / original_params * 100):+.1f}%")

